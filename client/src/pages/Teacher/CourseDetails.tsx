import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { ArrowLeft, Edit, Plus, RefreshCw, Eye, Settings, BarChart3 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { useGetCourseByIdQuery } from '@/redux/features/course/courseApi';
import { useGetLectureByCourseIdQuery } from '@/redux/features/lecture/lectureApi';
import { useGetMeQuery } from '@/redux/features/auth/authApi';
import { useAppDispatch } from '@/redux/hooks';
import { baseApi } from '@/redux/api/baseApi';
import { formatDuration } from '@/utils/formatDuration';
import ResponsiveLectureGrid from '@/components/Lecture/ResponsiveLectureGrid';

interface CourseDetailsProps {
  className?: string;
}

const CourseDetails: React.FC<CourseDetailsProps> = ({ className }) => {
  const { courseId } = useParams<{ courseId: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [searchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState(searchParams.get('tab') || 'overview');
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Get current user
  const { data: userData } = useGetMeQuery(undefined);
  const teacherId = userData?.data?._id;

  // Fetch course data with aggressive cache invalidation
  const {
    data: courseData,
    isLoading: courseLoading,
    error: courseError,
    refetch: refetchCourse
  } = useGetCourseByIdQuery(courseId || '', {
    skip: !courseId,
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  // Fetch lectures data
  const {
    data: lecturesData,
    isLoading: lecturesLoading,
    refetch: refetchLectures,
    isFetching: lecturesFetching
  } = useGetLectureByCourseIdQuery({ id: courseId || '' }, {
    skip: !courseId,
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  const course = courseData?.data;
  const lectures = lecturesData?.data || [];

  // Force cache invalidation on mount to ensure fresh data
  useEffect(() => {
    if (courseId && teacherId) {
      dispatch(baseApi.util.invalidateTags([
        'courses',
        'course',
        { type: 'course', id: courseId },
        { type: 'courses', id: 'LIST' },
        { type: 'courses', id: `creator-${teacherId}` },
        'lectures',
        { type: 'lectures', id: courseId }
      ]));
    }
  }, [courseId, teacherId, dispatch]);

  // Course statistics
  const courseStats = React.useMemo(() => {
    const totalDuration = lectures.reduce((acc: number, lecture: any) => acc + (lecture.duration || 0), 0);
    const publishedLectures = lectures.filter((l: any) => !l.isPreviewFree).length;
    const previewLectures = lectures.filter((l: any) => l.isPreviewFree).length;
    
    return {
      totalLectures: lectures.length,
      totalDuration,
      publishedLectures,
      previewLectures,
      enrolledStudents: course?.enrolledStudents?.length || 0,
    };
  }, [lectures, course]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      // Force comprehensive cache invalidation
      dispatch(baseApi.util.invalidateTags([
        'courses',
        'course',
        'lectures',
        'lecture',
        { type: 'courses', id: 'LIST' },
        { type: 'courses', id: 'published' },
        { type: 'courses', id: `creator-${teacherId}` },
        { type: 'course', id: courseId },
        { type: 'lectures', id: courseId }
      ]));

      await Promise.all([refetchCourse(), refetchLectures()]);
      toast.success("Course data refreshed successfully");
    } catch (error) {
      toast.error("Failed to refresh course data");
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleEditCourse = () => {
    navigate(`/teacher/courses/edit-course/${courseId}`);
  };

  const handleCreateLecture = () => {
    navigate(`/teacher/courses/${courseId}/lecture/create`);
  };

  const handleBackToCourses = () => {
    navigate('/teacher/courses');
  };

  const handlePreviewCourse = () => {
    window.open(`/courses/${courseId}`, '_blank');
  };

  if (courseLoading) {
    return <CourseDetailsSkeleton />;
  }

  if (courseError || !course) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Course not found
          </h3>
          <p className="text-gray-500 mb-6">
            The course you're looking for doesn't exist or you don't have permission to view it.
          </p>
          <Button onClick={handleBackToCourses}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Courses
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={handleBackToCourses}
            className="shrink-0"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Courses
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{course.title}</h1>
            <p className="text-gray-600">{course.subtitle}</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handlePreviewCourse}
          >
            <Eye className="w-4 h-4 mr-2" />
            Preview
          </Button>
          <Button
            size="sm"
            onClick={handleEditCourse}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Edit className="w-4 h-4 mr-2" />
            Edit Course
          </Button>
        </div>
      </div>

      {/* Course Status and Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Status</p>
                <Badge variant={course.isPublished ? "default" : "secondary"}>
                  {course.isPublished ? "Published" : "Draft"}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div>
              <p className="text-sm text-gray-600">Lectures</p>
              <p className="text-2xl font-bold">{courseStats.totalLectures}</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div>
              <p className="text-sm text-gray-600">Duration</p>
              <p className="text-2xl font-bold">{formatDuration(courseStats.totalDuration)}</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div>
              <p className="text-sm text-gray-600">Students</p>
              <p className="text-2xl font-bold">{courseStats.enrolledStudents}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="lectures">Lectures ({lectures.length})</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Course Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900">Title</h4>
                  <p className="text-gray-600">{course.title}</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Subtitle</h4>
                  <p className="text-gray-600">{course.subtitle || 'No subtitle'}</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Description</h4>
                  <p className="text-gray-600">{course.description || 'No description available'}</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Price</h4>
                  <p className="text-gray-600">${course.coursePrice || 'Free'}</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Level</h4>
                  <p className="text-gray-600">{course.courseLevel}</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  onClick={handleCreateLecture}
                  className="w-full justify-start bg-blue-600 hover:bg-blue-700"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add New Lecture
                </Button>
                <Button
                  variant="outline"
                  onClick={handleEditCourse}
                  className="w-full justify-start"
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Edit Course Details
                </Button>
                <Button
                  variant="outline"
                  onClick={handlePreviewCourse}
                  className="w-full justify-start"
                >
                  <Eye className="w-4 h-4 mr-2" />
                  Preview Course
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setActiveTab('settings')}
                  className="w-full justify-start"
                >
                  <Settings className="w-4 h-4 mr-2" />
                  Course Settings
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="lectures" className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Course Lectures</h3>
            <Button
              onClick={handleCreateLecture}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Lecture
            </Button>
          </div>
          
          {lecturesLoading || lecturesFetching ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <Skeleton key={i} className="h-20 w-full" />
              ))}
            </div>
          ) : lectures.length > 0 ? (
            <ResponsiveLectureGrid
              lectures={lectures}
              courseId={courseId || ''}
              onLectureAction={(action, lecture) => {
                if (action === 'edit') {
                  navigate(`/teacher/courses/${courseId}/lecture/edit/${lecture._id}`);
                } else if (action === 'preview') {
                  navigate(`/teacher/courses/${courseId}/lecture/preview/${lecture._id}`);
                }
              }}
            />
          ) : (
            <div className="text-center py-12">
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No lectures yet
              </h3>
              <p className="text-gray-500 mb-6">
                Start building your course by adding your first lecture.
              </p>
              <Button
                onClick={handleCreateLecture}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Create First Lecture
              </Button>
            </div>
          )}
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Course Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">
                Manage your course settings and preferences.
              </p>
              <Button
                onClick={handleEditCourse}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Settings className="w-4 h-4 mr-2" />
                Edit Course Settings
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Loading skeleton component
const CourseDetailsSkeleton = () => (
  <div className="container mx-auto px-4 py-6 space-y-6">
    <div className="flex justify-between items-center">
      <div className="space-y-2">
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-4 w-48" />
      </div>
      <div className="flex gap-2">
        <Skeleton className="h-9 w-20" />
        <Skeleton className="h-9 w-24" />
      </div>
    </div>
    
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      {[...Array(4)].map((_, i) => (
        <Skeleton key={i} className="h-20" />
      ))}
    </div>
    
    <div className="space-y-4">
      <Skeleton className="h-10 w-full" />
      <Skeleton className="h-64 w-full" />
    </div>
  </div>
);

export default CourseDetails;
